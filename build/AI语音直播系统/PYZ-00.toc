('E:\\IndexTTS-FAST\\build\\AI语音直播系统\\PYZ-00.pyz',
 [('PyQt5',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('_compat_pickle', 'D:\\python310\\lib\\_compat_pickle.py', 'PYMODULE'),
  ('_compression', 'D:\\python310\\lib\\_compression.py', 'PYMODULE'),
  ('_py_abc', 'D:\\python310\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\python310\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime', 'D:\\python310\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local', 'D:\\python310\\lib\\_threading_local.py', 'PYMODULE'),
  ('argparse', 'D:\\python310\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\python310\\lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\python310\\lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\python310\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\python310\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\python310\\lib\\calendar.py', 'PYMODULE'),
  ('contextlib', 'D:\\python310\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'D:\\python310\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'D:\\python310\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\python310\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'D:\\python310\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._endian', 'D:\\python310\\lib\\ctypes\\_endian.py', 'PYMODULE'),
  ('dataclasses', 'D:\\python310\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'D:\\python310\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\python310\\lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\python310\\lib\\dis.py', 'PYMODULE'),
  ('email', 'D:\\python310\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr', 'D:\\python310\\lib\\email\\_parseaddr.py', 'PYMODULE'),
  ('email._policybase',
   'D:\\python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime', 'D:\\python310\\lib\\email\\base64mime.py', 'PYMODULE'),
  ('email.charset', 'D:\\python310\\lib\\email\\charset.py', 'PYMODULE'),
  ('email.contentmanager',
   'D:\\python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders', 'D:\\python310\\lib\\email\\encoders.py', 'PYMODULE'),
  ('email.errors', 'D:\\python310\\lib\\email\\errors.py', 'PYMODULE'),
  ('email.feedparser', 'D:\\python310\\lib\\email\\feedparser.py', 'PYMODULE'),
  ('email.generator', 'D:\\python310\\lib\\email\\generator.py', 'PYMODULE'),
  ('email.header', 'D:\\python310\\lib\\email\\header.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators', 'D:\\python310\\lib\\email\\iterators.py', 'PYMODULE'),
  ('email.message', 'D:\\python310\\lib\\email\\message.py', 'PYMODULE'),
  ('email.parser', 'D:\\python310\\lib\\email\\parser.py', 'PYMODULE'),
  ('email.policy', 'D:\\python310\\lib\\email\\policy.py', 'PYMODULE'),
  ('email.quoprimime', 'D:\\python310\\lib\\email\\quoprimime.py', 'PYMODULE'),
  ('email.utils', 'D:\\python310\\lib\\email\\utils.py', 'PYMODULE'),
  ('fnmatch', 'D:\\python310\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\python310\\lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'D:\\python310\\lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\python310\\lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\python310\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\python310\\lib\\hashlib.py', 'PYMODULE'),
  ('importlib', 'D:\\python310\\lib\\importlib\\__init__.py', 'PYMODULE'),
  ('importlib._abc', 'D:\\python310\\lib\\importlib\\_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', 'D:\\python310\\lib\\importlib\\abc.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.util', 'D:\\python310\\lib\\importlib\\util.py', 'PYMODULE'),
  ('inspect', 'D:\\python310\\lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\python310\\lib\\ipaddress.py', 'PYMODULE'),
  ('logging', 'D:\\python310\\lib\\logging\\__init__.py', 'PYMODULE'),
  ('lzma', 'D:\\python310\\lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'D:\\python310\\lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\python310\\lib\\opcode.py', 'PYMODULE'),
  ('optparse', 'D:\\python310\\lib\\optparse.py', 'PYMODULE'),
  ('pathlib', 'D:\\python310\\lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\python310\\lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\python310\\lib\\pkgutil.py', 'PYMODULE'),
  ('pprint', 'D:\\python310\\lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'E:\\IndexTTS-FAST\\AI\\.venv\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'D:\\python310\\lib\\py_compile.py', 'PYMODULE'),
  ('quopri', 'D:\\python310\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\python310\\lib\\random.py', 'PYMODULE'),
  ('selectors', 'D:\\python310\\lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'D:\\python310\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\python310\\lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\python310\\lib\\socket.py', 'PYMODULE'),
  ('statistics', 'D:\\python310\\lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\python310\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\python310\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\python310\\lib\\subprocess.py', 'PYMODULE'),
  ('tarfile', 'D:\\python310\\lib\\tarfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\python310\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\python310\\lib\\threading.py', 'PYMODULE'),
  ('token', 'D:\\python310\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\python310\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'D:\\python310\\lib\\tracemalloc.py', 'PYMODULE'),
  ('typing', 'D:\\python310\\lib\\typing.py', 'PYMODULE'),
  ('urllib', 'D:\\python310\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.parse', 'D:\\python310\\lib\\urllib\\parse.py', 'PYMODULE'),
  ('uu', 'D:\\python310\\lib\\uu.py', 'PYMODULE'),
  ('zipfile', 'D:\\python310\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'D:\\python310\\lib\\zipimport.py', 'PYMODULE')])
