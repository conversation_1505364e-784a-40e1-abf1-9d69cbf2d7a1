('E:\\IndexTTS-FAST\\build\\AI语音直播系统_修复版\\PYZ-00.pyz',
 [('PyQt5',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\ast.py',
   'PYMODULE'),
  ('base64',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\base64.py',
   'PYMODULE'),
  ('bisect',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\calendar.py',
   'PYMODULE'),
  ('contextlib',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\decimal.py',
   'PYMODULE'),
  ('dis',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\dis.py',
   'PYMODULE'),
  ('email',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\fractions.py',
   'PYMODULE'),
  ('getopt',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\getopt.py',
   'PYMODULE'),
  ('gettext',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\gettext.py',
   'PYMODULE'),
  ('gzip',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\hashlib.py',
   'PYMODULE'),
  ('importlib',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('logging',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\lzma.py',
   'PYMODULE'),
  ('numbers',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\numbers.py',
   'PYMODULE'),
  ('opcode',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\opcode.py',
   'PYMODULE'),
  ('pathlib',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pickle',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\pickle.py',
   'PYMODULE'),
  ('pkgutil',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('pprint',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\pprint.py',
   'PYMODULE'),
  ('psutil',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\py_compile.py',
   'PYMODULE'),
  ('quopri',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\random.py',
   'PYMODULE'),
  ('selectors',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\selectors.py',
   'PYMODULE'),
  ('shutil',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\signal.py',
   'PYMODULE'),
  ('socket',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\socket.py',
   'PYMODULE'),
  ('statistics',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\threading.py',
   'PYMODULE'),
  ('token',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\typing.py',
   'PYMODULE'),
  ('urllib',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('zipfile',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\zipimport.py',
   'PYMODULE')])
