('E:\\IndexTTS-FAST\\build\\AI语音直播系统_修复版\\AI语音直播系统_修复版.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'E:\\IndexTTS-FAST\\build\\AI语音直播系统_修复版\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'E:\\IndexTTS-FAST\\build\\AI语音直播系统_修复版\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\IndexTTS-FAST\\build\\AI语音直播系统_修复版\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\IndexTTS-FAST\\build\\AI语音直播系统_修复版\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\IndexTTS-FAST\\build\\AI语音直播系统_修复版\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\IndexTTS-FAST\\build\\AI语音直播系统_修复版\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('launcher_preview',
   'E:\\IndexTTS-FAST\\AI\\launcher_preview.py',
   'PYSOURCE'),
  ('_ctypes.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\_ctypes.pyd',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('concrt140.dll', 'C:\\Windows\\System32\\concrt140.dll', 'BINARY'),
  ('msvcp140.dll', 'C:\\Windows\\System32\\msvcp140.dll', 'BINARY'),
  ('python312.dll',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\python312.dll',
   'BINARY'),
  ('vcruntime140.dll', 'C:\\Windows\\System32\\vcruntime140.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp312-win_amd64.pyd',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('ucrtbase.dll', 'C:\\Windows\\System32\\ucrtbase.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Windows\\System32\\VCRUNTIME140_1.dll', 'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\zlib.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('python3.dll',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\python3.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Windows\\System32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('app.ico', 'E:\\IndexTTS-FAST\\AI\\app.ico', 'DATA'),
  ('logo.png', 'E:\\IndexTTS-FAST\\AI\\logo.png', 'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('base_library.zip',
   'E:\\IndexTTS-FAST\\build\\AI语音直播系统_修复版\\base_library.zip',
   'DATA')],
 'python312.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
