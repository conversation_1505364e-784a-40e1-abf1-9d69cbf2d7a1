(['E:\\IndexTTS-FAST\\AI\\launcher_preview.py'],
 ['E:\\IndexTTS-FAST\\AI',
  'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312',
  'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs',
  'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib'],
 ['PyQt5.QtCore',
  'PyQt5.QtGui',
  'PyQt5.QtWidgets',
  'PyQt5.sip',
  'sip',
  'ctypes',
  'ctypes.wintypes',
  'psutil'],
 [('E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\playwright\\_impl\\__pyinstaller',
   0),
  ('E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['tkinter', 'matplotlib', 'numpy', 'scipy', '__main__'],
 [],
 False,
 {},
 0,
 [('_ctypes.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\_ctypes.pyd',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('concrt140.dll', 'C:\\Windows\\System32\\concrt140.dll', 'BINARY'),
  ('msvcp140.dll', 'C:\\Windows\\System32\\msvcp140.dll', 'BINARY'),
  ('python312.dll',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\python312.dll',
   'BINARY'),
  ('vcruntime140.dll', 'C:\\Windows\\System32\\vcruntime140.dll', 'BINARY')],
 [('app.ico', 'E:\\IndexTTS-FAST\\AI\\app.ico', 'DATA'),
  ('logo.png', 'E:\\IndexTTS-FAST\\AI\\logo.png', 'DATA')],
 '3.12.3 | packaged by conda-forge | (main, Apr 15 2024, 18:20:11) [MSC v.1938 '
 '64 bit (AMD64)]',
 [('pyi_rth_pyqt5',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('launcher_preview',
   'E:\\IndexTTS-FAST\\AI\\launcher_preview.py',
   'PYSOURCE')],
 [('zipfile',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('pathlib',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\pathlib.py',
   'PYMODULE'),
  ('urllib.parse',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ipaddress',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('fnmatch',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('contextlib',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\contextlib.py',
   'PYMODULE'),
  ('argparse',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\textwrap.py',
   'PYMODULE'),
  ('copy',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\copy.py',
   'PYMODULE'),
  ('gettext',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\gettext.py',
   'PYMODULE'),
  ('py_compile',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\typing.py',
   'PYMODULE'),
  ('importlib.abc',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\tempfile.py',
   'PYMODULE'),
  ('random',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\string.py',
   'PYMODULE'),
  ('bisect',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\bisect.py',
   'PYMODULE'),
  ('importlib._abc',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\getopt.py',
   'PYMODULE'),
  ('email.charset',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\calendar.py',
   'PYMODULE'),
  ('datetime',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_strptime',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_strptime.py',
   'PYMODULE'),
  ('socket',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\selectors.py',
   'PYMODULE'),
  ('quopri',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\quopri.py',
   'PYMODULE'),
  ('email',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\csv.py',
   'PYMODULE'),
  ('importlib.readers',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\token.py',
   'PYMODULE'),
  ('lzma',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\lzma.py',
   'PYMODULE'),
  ('_compression',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_compression.py',
   'PYMODULE'),
  ('bz2',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\bz2.py',
   'PYMODULE'),
  ('struct',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\struct.py',
   'PYMODULE'),
  ('shutil',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\gzip.py',
   'PYMODULE'),
  ('importlib.util',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\inspect.py',
   'PYMODULE'),
  ('dis',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\opcode.py',
   'PYMODULE'),
  ('ast',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\ast.py',
   'PYMODULE'),
  ('pkgutil',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\zipimport.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('PyQt5',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('tracemalloc',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('stringprep',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\stringprep.py',
   'PYMODULE'),
  ('ctypes',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('threading',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('psutil',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('signal',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\signal.py',
   'PYMODULE'),
  ('subprocess',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\subprocess.py',
   'PYMODULE')],
 [('_ctypes.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\_ctypes.pyd',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('concrt140.dll', 'C:\\Windows\\System32\\concrt140.dll', 'BINARY'),
  ('msvcp140.dll', 'C:\\Windows\\System32\\msvcp140.dll', 'BINARY'),
  ('python312.dll',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\python312.dll',
   'BINARY'),
  ('vcruntime140.dll', 'C:\\Windows\\System32\\vcruntime140.dll', 'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp312-win_amd64.pyd',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\sip.cp312-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('ucrtbase.dll', 'C:\\Windows\\System32\\ucrtbase.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll', 'C:\\Windows\\System32\\VCRUNTIME140_1.dll', 'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\zlib.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('python3.dll',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\python3.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Windows\\System32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Windows\\System32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('app.ico', 'E:\\IndexTTS-FAST\\AI\\app.ico', 'DATA'),
  ('logo.png', 'E:\\IndexTTS-FAST\\AI\\logo.png', 'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'E:\\IndexTTS-FAST\\AI\\backend\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('base_library.zip',
   'E:\\IndexTTS-FAST\\build\\AI语音直播系统_修复版\\base_library.zip',
   'DATA')],
 [('locale',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\locale.py',
   'PYMODULE'),
  ('heapq',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\heapq.py',
   'PYMODULE'),
  ('warnings',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\warnings.py',
   'PYMODULE'),
  ('traceback',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\traceback.py',
   'PYMODULE'),
  ('linecache',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\linecache.py',
   'PYMODULE'),
  ('copyreg',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\copyreg.py',
   'PYMODULE'),
  ('codecs',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\codecs.py',
   'PYMODULE'),
  ('ntpath',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\ntpath.py',
   'PYMODULE'),
  ('re._parser',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\re\\__init__.py',
   'PYMODULE'),
  ('sre_compile',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('functools',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\functools.py',
   'PYMODULE'),
  ('io', 'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\io.py', 'PYMODULE'),
  ('stat',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\stat.py',
   'PYMODULE'),
  ('_weakrefset',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('genericpath',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\genericpath.py',
   'PYMODULE'),
  ('weakref',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\weakref.py',
   'PYMODULE'),
  ('reprlib',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\reprlib.py',
   'PYMODULE'),
  ('abc',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\abc.py',
   'PYMODULE'),
  ('collections.abc',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('types',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\types.py',
   'PYMODULE'),
  ('enum',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\enum.py',
   'PYMODULE'),
  ('sre_constants',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('keyword',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\keyword.py',
   'PYMODULE'),
  ('operator',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\operator.py',
   'PYMODULE'),
  ('sre_parse',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\sre_parse.py',
   'PYMODULE'),
  ('posixpath',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\posixpath.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('_collections_abc',
   'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('os', 'E:\\IndexTTS-FAST\\index-tts-liuyue\\py312\\Lib\\os.py', 'PYMODULE')])
