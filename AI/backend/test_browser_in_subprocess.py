#!/usr/bin/env python3
"""
测试在subprocess环境中启动playwright浏览器
模拟一键启动器的环境
"""

import asyncio
import os
import sys
import subprocess
from playwright.async_api import async_playwright

# 修复Windows上的asyncio事件循环问题
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

async def test_browser_launch():
    """测试浏览器启动"""
    print("🔧 测试在subprocess环境中启动浏览器...")
    print(f"📁 当前工作目录: {os.getcwd()}")
    print(f"🐍 Python版本: {sys.version}")
    print(f"📦 Python路径: {sys.executable}")
    
    try:
        # 启动playwright
        playwright = await async_playwright().start()
        print("✅ Playwright启动成功")
        
        # 尝试启动浏览器
        print("🚀 正在启动浏览器...")
        browser = await playwright.chromium.launch(
            headless=False,
            args=['--no-sandbox', '--disable-dev-shm-usage']  # 添加一些兼容性参数
        )
        print("✅ 浏览器启动成功")
        
        # 创建页面
        page = await browser.new_page()
        print("✅ 页面创建成功")
        
        # 等待2秒
        await asyncio.sleep(2)
        
        # 关闭浏览器
        await browser.close()
        print("✅ 浏览器关闭成功")
        
        await playwright.stop()
        print("✅ 测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_browser_launch())
    if result:
        print("🎉 浏览器启动测试通过！")
    else:
        print("💥 浏览器启动测试失败！")
        sys.exit(1)
