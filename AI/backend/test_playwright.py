#!/usr/bin/env python3
"""
测试playwright是否能正常启动浏览器
用于诊断一键启动器和终端启动的差异
"""

import asyncio
import os
import sys
from playwright.async_api import async_playwright

# 修复Windows上的asyncio事件循环问题
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

async def test_playwright():
    """测试playwright浏览器启动"""
    print("🔧 开始测试playwright...")
    print(f"📁 当前工作目录: {os.getcwd()}")
    print(f"🐍 Python版本: {sys.version}")
    print(f"📦 Python路径: {sys.executable}")
    
    try:
        # 启动playwright
        playwright = await async_playwright().start()
        print("✅ Playwright启动成功")
        
        # 启动浏览器
        browser = await playwright.chromium.launch(headless=False)
        print("✅ 浏览器启动成功")
        
        # 创建页面
        page = await browser.new_page()
        print("✅ 页面创建成功")
        
        # 导航到测试页面
        await page.goto("https://www.baidu.com")
        print("✅ 页面导航成功")
        
        # 等待3秒让用户看到浏览器
        await asyncio.sleep(3)
        
        # 关闭浏览器
        await browser.close()
        print("✅ 浏览器关闭成功")
        
        await playwright.stop()
        print("✅ Playwright停止成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_playwright())
    if result:
        print("🎉 Playwright测试通过！")
    else:
        print("💥 Playwright测试失败！")
        sys.exit(1)
